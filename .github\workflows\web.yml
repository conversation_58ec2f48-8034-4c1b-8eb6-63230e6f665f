name: CI/CD for Web Deployment (VPS)
on:
  push:
    branches:
      - main

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v3

      - name: Build Web Application
        run: |
          npm install
          npm run build

      - name: Create Web Production Bundle
        run: |
          tar -czf dist-web.tar.gz dist

      - name: Setup SSH Key
        if: github.ref == 'refs/heads/main'
        run: |
          mkdir -p ~/.ssh
          echo "${{ secrets.SSH_PRIVATE_KEY }}" > ~/.ssh/id_rsa
          chmod 600 ~/.ssh/id_rsa
          ssh-keyscan -p 72 -H ${{ secrets.VPS_HOST }} >> ~/.ssh/known_hosts

      - name: Deploy Web to VPS
        if: github.ref == 'refs/heads/main'
        env:
          VPS_HOST: ${{ secrets.VPS_HOST }}
          VPS_USER: ${{ secrets.VPS_USER }}
        run: |
          # Copy web bundle using SSH key
          scp -P 72 dist-web.tar.gz $VPS_USER@$VPS_HOST:/tmp/
          
          # SSH into the VPS and extract the bundle
          ssh -p 72 $VPS_USER@$VPS_HOST << 'EOF'
            # Create and extract web files
            mkdir -p "/home/<USER>/gold"
            tar -xzf /tmp/dist-web.tar.gz -C "/home/<USER>/gold"
            rm /tmp/dist-web.tar.gz
          EOF

      - name: Cleanup SSH Key
        if: always()
        run: rm -f ~/.ssh/id_rsa