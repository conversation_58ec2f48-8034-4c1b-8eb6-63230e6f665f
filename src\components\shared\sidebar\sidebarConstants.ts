export const COLORS = {
  sidebarBackground:'#fffff',
  primaryBlue: "#6047E4", // Active background color
  secondaryBlue: "#2275FC", // Active icon/text color
  fadishBlack: "#5C5E64", // Default icon/text color
  gray50: "#f9fafb", // Hover background
  gray200: "#2275FC", // Border color
  gray300: "#d1d5db", // Hover background
  dividerGray: "#898989", // Section divider color
  white: "#ffffff", // Background color
  activeLinkBgColor:"#6047E4", // Active link background color
  activeLinkTextColor:"#ffffff", // Active link text color
  navHoverColor:'#6047E4', //Navbar hover color
};

// Text style variants
export const TEXT_STYLES = {
  "primary-blue": "text-[#2275FC] font-medium",
  "fadish-black": "text-[#5C5E64]",
  white: "text-white",
};

// Text sizes
export const TEXT_SIZES = {
  "body-sm-default": "text-sm",
  "body-xs-default": "text-xs",
};
