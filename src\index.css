@tailwind base;
@tailwind components;
@tailwind utilities;

@font-face {
  font-family: 'Dm Sans';
  src: url('/fonts/DMSans-Light.ttf') format('ttf');
  font-weight: 300;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: 'Dm Sans';
  src: url('/fonts/DMSans-Regular.ttf') format('ttf');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Dm Sans';
  src: url('/fonts/DMSans-Medium.ttf') format('ttf');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Dm Sans';
  src: url('/fonts/DMSans-SemiBold.ttf') format('ttf');
  font-weight: 600;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Dm Sans';
  src: url('/fonts/DMSans-Bold.ttf') format('ttf');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Dm Sans';
  src: url('/fonts/DMSans-ExtraBold.ttf') format('ttf');
  font-weight: 800;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Dm Sans';
  src: url('/fonts/DMSans-Black.ttf') format('ttf');
  font-weight: 900;
  font-style: normal;
  font-display: swap;
}

/* css reset */

*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}
* {
  margin: 0;
  font-family: 'Dm Sans' inherit;
  /* overflow: auto; */
  padding: 0;
}

/* Hide scrollbar globally (except ProductList) */
*::-webkit-scrollbar {
  display: none;
}

ol,
ul {
  list-style: none;
  margin: 0;
  padding: 0;
}


html,
body {
  height: 100%;
}
body {
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
}
img,
picture,
video,
canvas,
svg {
  display: block;
  max-width: 100%;
  cursor: pointer;
}
input,
button,
textarea,
select {
  font: inherit;
}
blockquote,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
figure,
p,
pre {
  word-wrap: break-word;
  margin: 0;
  box-sizing: border-box;
  text-decoration: none !important;
}
blockquote,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
figure,
p,
pre {
  word-wrap: break-word;
  margin: 0;
  box-sizing: border-box;
  text-decoration: none !important;
}

ul,
li {
  margin: 0;
  padding: 0;
}
li {
  list-style: disc;
}

#root,
#__next {
  isolation: isolate;
}

.center {
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
