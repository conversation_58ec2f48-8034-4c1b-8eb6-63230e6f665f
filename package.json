{"name": "gold-dashboard", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "prepare": "husky"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@radix-ui/react-tabs": "^1.1.3", "@tabler/icons-react": "^3.31.0", "@tanstack/react-form": "^1.2.1", "@tanstack/react-query": "^5.69.0", "@tanstack/react-store": "^0.7.0", "@types/crypto-js": "^4.2.2", "axios": "^1.8.4", "chart.js": "^4.4.8", "classnames": "^2.5.1", "clsx": "^2.1.1", "crypto-js": "^4.2.0", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "firebase": "^11.6.1", "formik": "^2.4.6", "graphql-request": "^7.1.2", "html2pdf.js": "^0.10.3", "js-cookie": "^3.0.5", "jwt-decode": "^4.0.0", "lodash": "^4.17.21", "lucide-react": "^0.483.0", "motion": "^12.6.3", "react": "^18.3.1", "react-chartjs-2": "^5.3.0", "react-dom": "^18.3.1", "react-dropzone": "^14.3.8", "react-fast-marquee": "^1.6.5", "react-router-dom": "^7.4.0", "react-slick": "^0.30.3", "react-to-print": "^3.0.6", "react-toastify": "^11.0.5", "slick-carousel": "^1.8.1", "tailwind-merge": "^3.2.0", "yup": "^1.6.1", "zod": "^3.24.2"}, "devDependencies": {"@eslint/js": "^9.21.0", "@iconify/react": "^5.2.0", "@types/js-cookie": "^3.0.6", "@types/lodash": "^4.17.16", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@types/react-slick": "^0.23.13", "@vitejs/plugin-react-swc": "^3.8.0", "autoprefixer": "^10.4.21", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "husky": "^9.1.7", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "typescript": "~5.7.2", "typescript-eslint": "^8.24.1", "vite": "6.2.6"}}