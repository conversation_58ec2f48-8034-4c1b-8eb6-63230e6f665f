interface ArrowIconProps {
    className?: string
    width?: number
    height?: number
}

export default function ArrowIcon({ className = "", width = 39, height = 26 }: ArrowIconProps) {
    return (
        <div className={`inline-block ${className}`} style={{ width, height }}>
            <svg
                width="100%"
                height="100%"
                viewBox="0 0 39 26"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
                className="w-full h-full"
            >
                <path
                    d="M0 25.3243V1H36.9369L24.3243 13.6126L36.9369 25.3243H0Z"
                    fill="#AC805D"
                    stroke="#AC805D"
                    strokeWidth="0.900901"
                />
                <g clipPath="url(#clip0_632_14684)">
                    <path
                        fillRule="evenodd"
                        clipRule="evenodd"
                        d="M15.4839 9.08374V9.07586C15.4863 9.05221 15.4942 8.9521 15.1994 8.79996C14.8557 8.62102 14.2755 8.48858 13.5132 8.48858C12.751 8.48858 12.1708 8.62181 11.8279 8.79917C11.5323 8.9521 11.5401 9.05142 11.5425 9.07586V9.08374C11.5401 9.10739 11.5323 9.2075 11.8279 9.35964C12.1708 9.53858 12.751 9.67102 13.5132 9.67102C14.2755 9.67102 14.8557 9.5378 15.1994 9.36043C15.4942 9.2075 15.4863 9.10818 15.4839 9.08374ZM16.6664 9.0798C16.6664 9.66156 16.3251 10.0998 15.7709 10.3955L16.4212 12.2164C17.6454 12.5948 18.44 13.2569 18.44 14.2029C18.44 15.6644 16.5466 16.4479 14.1044 16.5559V19.1305C14.1044 19.2873 14.0422 19.4377 13.9313 19.5485C13.8204 19.6594 13.67 19.7217 13.5132 19.7217C13.3564 19.7217 13.206 19.6594 13.0952 19.5485C12.9843 19.4377 12.922 19.2873 12.922 19.1305V16.5559C10.4799 16.4487 8.58643 15.6644 8.58643 14.2037C8.58643 13.2577 9.38102 12.5956 10.6052 12.2172L11.2556 10.3955C10.7014 10.0998 10.3601 9.66156 10.3601 9.0798C10.3601 7.89737 11.7711 7.30615 13.5132 7.30615C15.2553 7.30615 16.6664 7.89737 16.6664 9.0798Z"
                        fill="#FAFAFF"
                    />
                </g>
                <defs>
                    <clipPath id="clip0_632_14684">
                        <rect width="12.6126" height="12.6126" fill="white" transform="translate(7.20703 7.30615)" />
                    </clipPath>
                </defs>
            </svg>
        </div>
    )
}

