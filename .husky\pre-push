#!/bin/sh
. "$(dirname "$0")/_/husky.sh"

echo "🚀 Running pre-push checks..."
# Store the exit code
EXIT_CODE=0

echo "🔍 Running build..."
if ! npm run build; then
  echo "❌ Build failed"
  EXIT_CODE=1
else
  echo "✅ Build succeeded"
fi

# Add other checks here if needed (like tests)
# echo "🔍 Running tests..."
# if ! npm test; then
#   echo "❌ Tests failed"
#   EXIT_CODE=1
# else
#   echo "✅ Tests passed"
# fi

if [ $EXIT_CODE -ne 0 ]; then
  echo "🛑 Push aborted due to failures"
  exit $EXIT_CODE
fi

echo "🚀 All checks passed - pushing to remote"
exit 0