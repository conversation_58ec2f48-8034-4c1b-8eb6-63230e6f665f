import React, { useState } from "react";
import Header from "../../../components/shared/table_heading/Header";
import { Icon } from "@iconify/react/dist/iconify.js";
import SupplierCard from "../components/SupplierDetailCard";
import ContactCard from "../components/ContactCard";
import { useNavigate } from "react-router-dom";
import { FrontendRoutes } from "../../../routes/routes";
import { Table } from "../../../components/shared";
import { ColumnConfig, TableData } from "../../../components/shared/Table";

const itemSupplied = [
  {
    id: 1,
    text: "Necklaces",
  },
  {
    id: 2,
    text: "Earrings",
  },
  {
    id: 3,
    text: "Bracelets",
  },
  {
    id: 4,
    text: "Brooches",
  },
  {
    id: 5,
    text: "Anklets",
  },
  {
    id: 6,
    text: "Cufflinks",
  },
  {
    id: 7,
    text: "Hairpins",
  },
];

const contactDataList = [
  {
    title: "Contact Information",
    contacts: [
      { subtitle: "Contact Person", text: "Sundarlal Gurung" },
      { subtitle: "Contact Number", text: "+977-**********" },
      { subtitle: "Email Address", text: "<EMAIL>" },
    ],
  },
  {
    title: "Business Information",
    contacts: [
      { subtitle: "Business Type", text: "Agricultural Produce Supplier" },
      { subtitle: "Company Registration Number", text: "*********" },
      { subtitle: "Permanent Account Number", text: "*********" },
    ],
  },
  {
    title: "Delivery Information",
    contacts: [
      { subtitle: "Frequency", text: "Twice a week" },
      { subtitle: "Delivery Time", text: "9:00 AM to 11:00 AM" },
      { subtitle: "Lead Time", text: "3-5 business days" },
    ],
  },
];
const SupplierHistory = () => {
  const pinkItems = ["Necklaces", "Earrings", "Bracelets"];
  const navigate = useNavigate();

  const handleButtonClick = () => {
    navigate(`${FrontendRoutes.SUPPLIERDETAIL}`);
  };

  interface SupplierHistoryItem {
    _id: string;
    purchase_id: string;
    purchase_date: string;
    total_amount: string;
    paid: string;
    due: string;
    qty: number;
  }
  const [selectedFilter, setSelectedFilter] = useState("");
  const [selectedSort, setSelectedSort] = useState("");
  const [searchQuery, setSearchQuery] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    setCurrentPage(1);
  };

  const handleFilter = (filter: string) => {
    setSelectedFilter(filter);
    setCurrentPage(1);
  };

  const handleSort = (sort: string) => {
    setSelectedSort(sort);
    setCurrentPage(1);
  };

  const handleViewItem = (item: TableData<SupplierHistoryItem>) => {
    navigate(`${FrontendRoutes.CUSTOMERDETAILS}`);
  };

  const handleEditItem = (item: TableData<SupplierHistoryItem>) => {
    // // Check which tab is selected and set the editing item accordingly
    // if (selectedTab === "home_page") {
    //   setEditingItem(item); // Set the item for the Home Page Banner
    // } else if (selectedTab === "other_page") {
    //   setEditingItem(item); // Set the item for the Other Page Banner
    // }
    setIsModalOpen(true); // Open the modal for either banner
  };

  const handleDeleteItem = (item: TableData<SupplierHistoryItem>) => {
    console.log("Delete item:", item);
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleItemsPerPageChange = (limit: number) => {
    setItemsPerPage(limit);
    setCurrentPage(1);
  };
  const filterItems = (items: SupplierHistoryItem[]) => {
    return items.filter(
      (item) =>
        item.purchase_date.toLowerCase().includes(searchQuery.toLowerCase()) ||
        item._id?.toLowerCase().includes(searchQuery.toLowerCase())
    );
  };

  const columns: ColumnConfig<SupplierHistoryItem>[] = [
    { header: "Purchase ID", key: "purchase_id" },
    { header: "Purchase Date", key: "purchase_date" },
    { header: "Total Amount", key: "total_amount" },
    { header: "Paid", key: "paid" },
    { header: "Due", key: "due" },
    { header: "QTY", key: "qty" },
  ];

  const [supplierHistoryItems] = useState<SupplierHistoryItem[]>([
    {
      _id: "1",
      purchase_id: "P-4565",
      purchase_date: "2025-01-25",
      total_amount: "Rs.450,000",
      paid: "Rs.45,000",
      due: "Rs.45",
      qty: 50,
    },
    {
      _id: "2",
      purchase_id: "P-4565",
      purchase_date: "2025-01-25",
      total_amount: "Rs.450,000",
      paid: "Rs.45,000",
      due: "Rs.45",
      qty: 50,
    },
    {
      _id: "3",
      purchase_id: "P-4565",
      purchase_date: "2025-01-25",
      total_amount: "Rs.450,000",
      paid: "Rs.45,000",
      due: "Rs.45",
      qty: 50,
    },
    {
      _id: "4",
      purchase_id: "P-4565",
      purchase_date: "2025-01-25",
      total_amount: "Rs.450,000",
      paid: "Rs.45,000",
      due: "Rs.45",
      qty: 50,
    },
  ]);

  const renderSupplierHistoryTable = (items: SupplierHistoryItem[]) => {
    const filteredItems = filterItems(items);
    return (
      <Table
        columns={columns}
        data={filteredItems}
        showEdit={false}
        showDelete={false}
        showView={true}
        showAction={true}
        showSelectAll={true}
        viewRow={handleViewItem}
        editRow={handleEditItem}
        deleteRow={handleDeleteItem}
        showPagination={true}
        paginationDetails={{
          currentPage,
          limit: itemsPerPage,
          totalCount: filteredItems.length,
        }}
        onItemsPerPageChange={handleItemsPerPageChange}
        onPageChange={handlePageChange}
        showBg={true}
      />
    );
  };
  return (
    <>
      <div className="container mx-auto px-8 py-3">
        <div className="mb-4">
          <Header text="Supplier History" />
        </div>

        <div className="mb-8">
          <SupplierCard
            image="/test_logo.jpg"
            title="Opulent Jewel House"
            supplierType="Gold Supplier"
            location="SundarBasti, Bhangal, Kathmandu 44600"
            itemsSupplied={[
              "Necklaces",
              "Earrings",
              "Bracelets",
              "Brooches",
              "Anklets",
              "Cufflinks",
              "Hairpins",
            ]}
            buttonText="Basic Information"
            buttonClick={handleButtonClick}
          />
        </div>

        <div className="bg-white rounded-xl">
          {renderSupplierHistoryTable(supplierHistoryItems)}
        </div>
      </div>
    </>
  );
};

export default SupplierHistory;
